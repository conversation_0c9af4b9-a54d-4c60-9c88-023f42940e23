cmake_minimum_required(VERSION 3.0.2)
project(cpp_version)

# 设置C++标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找ROS包 - 只要基本的
find_package(catkin REQUIRED COMPONENTS
  roscpp
  std_msgs
  geometry_msgs
  gazebo_msgs
  sensor_msgs
)

# 查找基本库
find_package(Boost REQUIRED COMPONENTS system thread)
find_package(PkgConfig REQUIRED)
pkg_check_modules(MOSQUITTO REQUIRED libmosquitto)

# 查找nlohmann/json库
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
  # 如果系统没有安装nlohmann/json，尝试使用pkg-config
  pkg_check_modules(NLOHMANN_JSON nlohmann_json)
  if(NOT NLOHMANN_JSON_FOUND)
    message(WARNING "nlohmann/json not found via pkg-config, assuming header-only installation")
  endif()
endif()

# catkin包配置 - 最简单的
catkin_package(
  CATKIN_DEPENDS roscpp std_msgs geometry_msgs gazebo_msgs sensor_msgs
)

# 包含目录
include_directories(
  include
  ${catkin_INCLUDE_DIRS}
  ${MOSQUITTO_INCLUDE_DIRS}
  ${NLOHMANN_JSON_INCLUDE_DIRS}
)

# 主要可执行文件 - 完全对应Python版本的命名
add_executable(CarSim src/CarSim.cpp)
target_link_libraries(CarSim
  ${catkin_LIBRARIES}
  ${MOSQUITTO_LIBRARIES}
  ${NLOHMANN_JSON_LIBRARIES}
)

# 如果找到了nlohmann_json的CMake配置，链接它
if(nlohmann_json_FOUND)
  target_link_libraries(CarSim nlohmann_json::nlohmann_json)
endif()

add_executable(VelControl src/VelControl.cpp)
target_link_libraries(VelControl ${catkin_LIBRARIES})

add_executable(car_led_ctrl
  src/car_led_ctrl.cpp
  src/led/led_controller.cpp
  src/led/local_led_controller.cpp
)
target_link_libraries(car_led_ctrl ${catkin_LIBRARIES})

add_executable(car_noled src/car_noled.cpp)
target_link_libraries(car_noled ${catkin_LIBRARIES})

# 工具程序 - 对应Python版本的命名
add_executable(interactive_led_control src/interactive_led_control.cpp)
target_link_libraries(interactive_led_control ${catkin_LIBRARIES})

add_executable(Set_Car_Model_State_Simple src/Set_Car_Model_State_Simple.cpp)
target_link_libraries(Set_Car_Model_State_Simple ${catkin_LIBRARIES})

# 灯语响应集群动作实验程序
add_executable(swarm_light_experiment src/swarm_light_experiment.cpp)
target_link_libraries(swarm_light_experiment ${catkin_LIBRARIES})

# Xbox手柄本地控制器
add_executable(xbox_controller_local src/xbox_controller_local.cpp)
target_link_libraries(xbox_controller_local ${catkin_LIBRARIES})

# 安装 - 使用与Python版本一致的命名
install(TARGETS CarSim VelControl car_led_ctrl car_noled interactive_led_control Set_Car_Model_State_Simple swarm_light_experiment xbox_controller_local
  RUNTIME DESTINATION ${CATKIN_PACKAGE_BIN_DESTINATION}
)
