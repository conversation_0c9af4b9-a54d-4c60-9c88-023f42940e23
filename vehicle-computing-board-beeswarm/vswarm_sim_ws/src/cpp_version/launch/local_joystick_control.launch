<?xml version="1.0"?>
<launch>
  <!-- 本地Xbox手柄控制启动文件 -->
  <!-- 只控制当前主机的车辆，不影响其他车辆 -->
  
  <!-- 获取主机名参数 -->
  <arg name="hostname" default="$(optenv HOSTNAME vswarm11)"/>
  <arg name="joy_device" default="/dev/input/js0"/>
  <arg name="max_linear_vel" default="1.0"/>
  <arg name="max_angular_vel" default="1.0"/>
  
  <!-- 启动joy节点 - 读取Xbox手柄输入 -->
  <node name="joy_node" pkg="joy" type="joy_node" output="screen">
    <param name="dev" value="$(arg joy_device)"/>
    <param name="deadzone" value="0.05"/>
    <param name="autorepeat_rate" value="20"/>
  </node>
  
  <!-- 启动本地手柄控制节点 -->
  <node name="local_joystick_controller" pkg="cpp_version" type="local_joystick_control" output="screen">
    <param name="max_linear_vel" value="$(arg max_linear_vel)"/>
    <param name="max_angular_vel" value="$(arg max_angular_vel)"/>
    <remap from="~vel_cmd" to="/$(arg hostname)/vel_cmd"/>
    <remap from="~led_mode" to="/$(arg hostname)/led_mode"/>
  </node>
  
  <!-- 显示启动信息 -->
  <node name="joystick_info" pkg="rostopic" type="rostopic" 
        args="echo /joy -n 1" output="screen" launch-prefix="bash -c 'sleep 2; echo 手柄控制启动完成，控制车辆: $(arg hostname); $0 $@'" />
  
</launch>
