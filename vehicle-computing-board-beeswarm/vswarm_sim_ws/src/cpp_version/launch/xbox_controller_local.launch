<?xml version="1.0"?>
<launch>
  <!-- Xbox手柄本地控制启动文件 -->
  <!-- 只控制当前主机的车辆，不通过MQTT -->
  
  <!-- 获取主机名参数 -->
  <arg name="hostname" default="$(optenv HOSTNAME vswarm11)"/>
  <arg name="debug" default="false"/>
  
  <!-- 启动joy节点 - 读取Xbox手柄输入 -->
  <node name="joy_node" pkg="joy" type="joy_node" output="screen">
    <param name="dev" value="/dev/input/js0" />
    <param name="deadzone" value="0.05" />
    <param name="autorepeat_rate" value="20" />
  </node>
  
  <!-- 启动Xbox手柄本地控制器 -->
  <node name="xbox_controller_local" pkg="cpp_version" type="xbox_controller_local" output="screen">
    <remap from="~vel_cmd" to="/$(arg hostname)/vel_cmd"/>
    <remap from="~led_mode" to="/$(arg hostname)/led_mode"/>
  </node>
  
  <!-- 调试工具（可选） -->
  <group if="$(arg debug)">
    <!-- 监控手柄输入 -->
    <node name="rostopic_echo_joy" pkg="rostopic" type="rostopic" args="echo /joy" output="screen"/>
    
    <!-- 监控发送的命令 -->
    <node name="rostopic_echo_vel" pkg="rostopic" type="rostopic" args="echo /$(arg hostname)/vel_cmd" output="screen"/>
    <node name="rostopic_echo_led" pkg="rostopic" type="rostopic" args="echo /$(arg hostname)/led_mode" output="screen"/>
  </group>
  
</launch>
