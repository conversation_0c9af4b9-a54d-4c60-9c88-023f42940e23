// Xbox手柄本地控制器 - 只控制当前主机的车辆
// 类似于Set_Car_Model_State_Simple.cpp，但使用手柄输入而不是键盘输入
#include <iostream>
#include <string>
#include <unistd.h>
#include <ros/ros.h>
#include <sensor_msgs/Joy.h>
#include <geometry_msgs/Twist.h>
#include <std_msgs/Int32.h>

class XboxControllerLocal {
public:
    XboxControllerLocal() {
        // 获取主机名作为模型名
        char hostname[256];
        gethostname(hostname, sizeof(hostname));
        model_name_ = std::string(hostname);
        
        // 初始化ROS
        ros::NodeHandle nh;
        
        // 创建发布者 - 直接发布到本地车辆的话题
        vel_pub_ = nh.advertise<geometry_msgs::Twist>("/" + model_name_ + "/vel_cmd", 10);
        led_pub_ = nh.advertise<std_msgs::Int32>("/" + model_name_ + "/led_mode", 10);
        
        // 创建订阅者 - 订阅手柄输入
        joy_sub_ = nh.subscribe("/joy", 10, &XboxControllerLocal::joyCallback, this);
        
        // 初始化控制参数
        max_linear_vel_ = 1.0;
        max_angular_vel_ = 1.0;
        deadband_value_ = 0.05;
        led_mode_ = 0;
        last_button_time_ = ros::Time::now();
        
        std::cout << "Xbox Controller Local started for model: " << model_name_ << std::endl;
        std::cout << "Publishing to topics:" << std::endl;
        std::cout << "  Velocity: /" << model_name_ << "/vel_cmd" << std::endl;
        std::cout << "  LED: /" << model_name_ << "/led_mode" << std::endl;
        std::cout << "Controls:" << std::endl;
        std::cout << "  Left stick: Move vehicle (forward/backward/turn)" << std::endl;
        std::cout << "  A button: Cycle LED modes" << std::endl;
        std::cout << "  B button: Stop vehicle" << std::endl;
    }
    
    void joyCallback(const sensor_msgs::Joy::ConstPtr& msg) {
        // 处理按钮输入
        handleButtons(msg);
        
        // 处理摇杆输入
        handleSticks(msg);
    }
    
    void run() {
        ros::Rate rate(20);  // 20Hz
        
        while (ros::ok()) {
            ros::spinOnce();
            rate.sleep();
        }
    }
    
private:
    void handleButtons(const sensor_msgs::Joy::ConstPtr& msg) {
        ros::Time current_time = ros::Time::now();
        
        // A按钮 (按钮0) - 切换LED模式
        if (msg->buttons.size() > 0 && msg->buttons[0] == 1) {
            if (current_time - last_button_time_ > ros::Duration(0.5)) {
                led_mode_ = (led_mode_ + 1) % 4;  // 循环0-3
                
                std_msgs::Int32 led_msg;
                led_msg.data = led_mode_;
                led_pub_.publish(led_msg);
                
                std::cout << "LED mode changed to: " << led_mode_ << std::endl;
                last_button_time_ = current_time;
            }
        }
        
        // B按钮 (按钮1) - 紧急停止
        if (msg->buttons.size() > 1 && msg->buttons[1] == 1) {
            if (current_time - last_button_time_ > ros::Duration(0.3)) {
                geometry_msgs::Twist stop_msg;
                stop_msg.linear.x = 0.0;
                stop_msg.linear.y = 0.0;
                stop_msg.angular.z = 0.0;
                vel_pub_.publish(stop_msg);
                
                std::cout << "Emergency stop!" << std::endl;
                last_button_time_ = current_time;
            }
        }
    }
    
    void handleSticks(const sensor_msgs::Joy::ConstPtr& msg) {
        if (msg->axes.size() < 2) {
            return;
        }
        
        // 左摇杆控制
        // axes[1] = 前进/后退 (上/下)
        // axes[0] = 左转/右转 (左/右)
        double linear_x = applyDeadband(msg->axes[1]) * max_linear_vel_;
        double angular_z = applyDeadband(msg->axes[0]) * max_angular_vel_;
        
        // 只有在有实际输入时才发布
        if (abs(linear_x) > 0.01 || abs(angular_z) > 0.01) {
            geometry_msgs::Twist vel_msg;
            vel_msg.linear.x = linear_x;
            vel_msg.linear.y = 0.0;
            vel_msg.angular.z = angular_z;
            
            vel_pub_.publish(vel_msg);
            
            // 打印控制信息（降低频率）
            static int print_counter = 0;
            if (++print_counter % 10 == 0) {  // 每10次打印一次
                std::cout << "Control: linear_x=" << linear_x 
                         << ", angular_z=" << angular_z << std::endl;
            }
        }
    }
    
    double applyDeadband(double value) {
        if (abs(value) < deadband_value_) {
            return 0.0;
        }
        return value;
    }
    
private:
    std::string model_name_;
    ros::Publisher vel_pub_;
    ros::Publisher led_pub_;
    ros::Subscriber joy_sub_;
    
    double max_linear_vel_;
    double max_angular_vel_;
    double deadband_value_;
    int led_mode_;
    ros::Time last_button_time_;
};

int main(int argc, char** argv) {
    // 初始化ROS节点
    ros::init(argc, argv, "xbox_controller_local");
    
    try {
        XboxControllerLocal controller;
        controller.run();
    } catch (const std::exception& e) {
        ROS_ERROR("Exception: %s", e.what());
        return 1;
    }
    
    return 0;
}
