// 本地Xbox手柄控制节点 - 只控制当前主机的车辆
// 功能：直接通过ROS话题控制本地车辆，不通过MQTT系统
#include <ros/ros.h>
#include <sensor_msgs/Joy.h>
#include <geometry_msgs/Twist.h>
#include <std_msgs/Int32.h>
#include <unistd.h>
#include <iostream>
#include <string>

class LocalJoystickController {
public:
    LocalJoystickController() {
        // 获取主机名作为车辆名称
        char hostname[256];
        gethostname(hostname, sizeof(hostname));
        vehicle_name_ = std::string(hostname);
        
        // 初始化ROS
        ros::NodeHandle nh;
        
        // 创建发布者 - 直接发布到本车的控制话题
        vel_pub_ = nh.advertise<geometry_msgs::Twist>("/" + vehicle_name_ + "/vel_cmd", 10);
        led_pub_ = nh.advertise<std_msgs::Int32>("/" + vehicle_name_ + "/led_mode", 10);
        
        // 订阅手柄输入
        joy_sub_ = nh.subscribe("/joy", 10, &LocalJoystickController::joyCallback, this);
        
        // 初始化参数
        deadband_value_ = 0.05;
        max_linear_vel_ = 1.0;
        max_angular_vel_ = 1.0;
        led_mode_ = 0;
        last_button_time_ = ros::Time::now();
        
        std::cout << "本地Xbox手柄控制器启动 - 控制车辆: " << vehicle_name_ << std::endl;
        std::cout << "发布话题:" << std::endl;
        std::cout << "  速度控制: /" << vehicle_name_ << "/vel_cmd" << std::endl;
        std::cout << "  LED控制: /" << vehicle_name_ << "/led_mode" << std::endl;
        std::cout << "手柄控制说明:" << std::endl;
        std::cout << "  左摇杆: 控制车辆前进后退和转向" << std::endl;
        std::cout << "  A按钮: 切换LED模式" << std::endl;
        std::cout << "  B按钮: 停止车辆运动" << std::endl;
    }
    
    void joyCallback(const sensor_msgs::Joy::ConstPtr& msg) {
        // 处理按钮输入
        handleButtons(msg);
        
        // 处理摇杆输入
        handleSticks(msg);
    }
    
private:
    void handleButtons(const sensor_msgs::Joy::ConstPtr& msg) {
        ros::Time current_time = ros::Time::now();
        
        // A按钮 - 切换LED模式（防抖动）
        if (msg->buttons[0] && (current_time - last_button_time_).toSec() > 0.5) {
            led_mode_ = (led_mode_ + 1) % 4;  // 循环切换0-3模式
            
            std_msgs::Int32 led_msg;
            led_msg.data = led_mode_;
            led_pub_.publish(led_msg);
            
            std::cout << "LED模式切换到: " << led_mode_ << std::endl;
            last_button_time_ = current_time;
        }
        
        // B按钮 - 紧急停止
        if (msg->buttons[1] && (current_time - last_button_time_).toSec() > 0.5) {
            geometry_msgs::Twist stop_msg;
            stop_msg.linear.x = 0.0;
            stop_msg.linear.y = 0.0;
            stop_msg.angular.z = 0.0;
            vel_pub_.publish(stop_msg);
            
            std::cout << "紧急停止车辆" << std::endl;
            last_button_time_ = current_time;
        }
    }
    
    void handleSticks(const sensor_msgs::Joy::ConstPtr& msg) {
        // 处理左摇杆输入（axes[1]为前后，axes[0]为左右转向）
        double linear_x = applyDeadband(msg->axes[1]) * max_linear_vel_;
        double angular_z = applyDeadband(msg->axes[0]) * max_angular_vel_;
        
        // 只有在有实际输入时才发布速度命令
        if (std::abs(linear_x) > 0.01 || std::abs(angular_z) > 0.01) {
            geometry_msgs::Twist vel_msg;
            vel_msg.linear.x = linear_x;
            vel_msg.linear.y = 0.0;
            vel_msg.linear.z = 0.0;
            vel_msg.angular.x = 0.0;
            vel_msg.angular.y = 0.0;
            vel_msg.angular.z = angular_z;
            
            vel_pub_.publish(vel_msg);
            
            // 调试输出（可选）
            if (std::abs(linear_x) > 0.1 || std::abs(angular_z) > 0.1) {
                std::cout << "控制 " << vehicle_name_ 
                         << " - 线速度: " << linear_x 
                         << ", 角速度: " << angular_z << std::endl;
            }
        }
    }
    
    double applyDeadband(double value) {
        if (std::abs(value) < deadband_value_) {
            return 0.0;
        }
        return value;
    }
    
    // 成员变量
    std::string vehicle_name_;
    ros::Publisher vel_pub_;
    ros::Publisher led_pub_;
    ros::Subscriber joy_sub_;
    
    double deadband_value_;
    double max_linear_vel_;
    double max_angular_vel_;
    int led_mode_;
    ros::Time last_button_time_;
};

int main(int argc, char** argv) {
    ros::init(argc, argv, "local_joystick_controller");
    
    try {
        LocalJoystickController controller;
        
        std::cout << "本地手柄控制器运行中..." << std::endl;
        std::cout << "请确保已连接Xbox手柄并启动了joy_node" << std::endl;
        
        ros::spin();
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
