# Xbox手柄本地控制器使用说明

## 功能说明

这个Xbox手柄控制器专门用于**本地控制**当前主机上的车辆，类似于 `Set_Car_Model_State_Simple.cpp` 的控制方式，但使用Xbox手柄而不是键盘输入。

### 特点
- **本地控制**：只控制当前主机名对应的车辆（如VSWARM11）
- **直接ROS通信**：不通过MQTT，直接发布到ROS话题
- **实时响应**：20Hz控制频率，响应迅速
- **简单易用**：即插即用，无需复杂配置

## 控制说明

### Xbox手柄按键映射（全向运动）
- **左摇杆**：
  - 上/下：车辆前进/后退（linear.x）
  - 左/右：车辆左移/右移（linear.y）
- **右摇杆**：
  - 左/右：车辆左转/右转（angular.z）
- **A按钮**：循环切换LED模式（0→1→2→3→0...）
- **B按钮**：紧急停止车辆

### 控制参数
- 最大线速度：1.0 m/s
- 最大角速度：1.0 rad/s
- 死区值：0.05（消除摇杆漂移）

## 使用方法

### 1. 编译程序
```bash
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
catkin_make
source devel/setup.bash
```

### 2. 连接Xbox手柄
确保Xbox手柄已连接到电脑，并且在 `/dev/input/js0` 可以检测到。

### 3. 启动车辆控制系统
```bash
# 启动Gazebo仿真环境
roslaunch vswarm_sim car_swarm.launch

# 启动C++车辆控制（新终端）
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
roslaunch vswarm_sim start_car_cpp.launch
```

### 4. 启动Xbox手柄控制器

#### 方法A：使用launch文件（推荐）
```bash
# 新终端
cd ~/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws
source devel/setup.bash
roslaunch cpp_version xbox_controller_local.launch
```

#### 方法B：手动启动
```bash
# 启动joy节点
rosrun joy joy_node

# 启动Xbox控制器（新终端）
rosrun cpp_version xbox_controller_local
```

## 调试和测试

### 检查手柄连接
```bash
# 检查手柄设备
ls /dev/input/js*

# 测试手柄输入
rostopic echo /joy
```

### 监控控制命令
```bash
# 监控速度命令（应该能看到linear.x, linear.y, angular.z）
rostopic echo /$(hostname)/vel_cmd

# 监控LED命令
rostopic echo /$(hostname)/led_mode
```

### 手动测试
```bash
# 手动发送速度命令
rostopic pub /$(hostname)/vel_cmd geometry_msgs/Twist "linear: {x: 0.5, y: 0.0, z: 0.0}"

# 手动发送LED命令
rostopic pub /$(hostname)/led_mode std_msgs/Int32 "data: 1"
```

## 故障排除

### 1. 手柄无响应
- 检查手柄是否正确连接：`ls /dev/input/js*`
- 检查joy节点是否运行：`rosnode list | grep joy`
- 检查joy话题是否有数据：`rostopic echo /joy`

### 2. 车辆不动
- 检查车辆控制节点是否运行：`rosnode list | grep VelControl`
- 检查速度话题是否有订阅者：`rostopic info /$(hostname)/vel_cmd`
- 检查Gazebo是否正常运行

### 3. LED不变化
- 检查LED控制节点是否运行：`rosnode list | grep led`
- 检查LED话题是否有订阅者：`rostopic info /$(hostname)/led_mode`

## 与其他控制方式的区别

| 控制方式 | 作用范围 | 通信方式 | 适用场景 |
|---------|---------|---------|---------|
| `agent_management.py` | 所有车辆 | MQTT | 多车协调控制 |
| `xbox_controller_local` | 本地车辆 | ROS直连 | 单车精确控制 |
| `Set_Car_Model_State_Simple` | 本地车辆 | ROS直连 | 位置设置 |

## 注意事项

1. **单车控制**：此控制器只控制当前主机的车辆，不影响其他车辆
2. **实时性**：直接ROS通信，延迟更低，适合精确控制
3. **安全性**：B按钮可以紧急停止车辆
4. **兼容性**：与现有的C++车辆控制系统完全兼容
