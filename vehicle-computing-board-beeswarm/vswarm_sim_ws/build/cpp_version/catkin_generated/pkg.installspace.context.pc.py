# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "".split(';') if "" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;std_msgs;geometry_msgs;gazebo_msgs;sensor_msgs".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "".split(';') if "" != "" else []
PROJECT_NAME = "cpp_version"
PROJECT_SPACE_DIR = "/home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/install"
PROJECT_VERSION = "1.0.0"
