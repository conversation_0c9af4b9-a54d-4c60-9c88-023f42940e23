# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/src/cpp_version/src/xbox_controller_local.cpp
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/geometry_msgs/Twist.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/geometry_msgs/Vector3.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/advertise_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/advertise_service_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/assert.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/builtin_message_traits.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/common.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/console.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/console_backend.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/datatypes.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/duration.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/exception.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/exceptions.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/forwards.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/init.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/macros.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/master.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/message.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/message_event.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/message_forward.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/message_operations.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/message_traits.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/names.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/node_handle.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/param.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/parameter_adapter.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/platform.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/publisher.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/rate.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/ros.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/roscpp_serialization_macros.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/rostime_decl.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/serialization.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/serialized_message.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service_callback_helper.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service_client.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service_client_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service_server.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/service_traits.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/single_subscriber_publisher.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/spinner.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/static_assert.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/steady_timer.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/steady_timer_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/subscribe_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/subscriber.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/subscription_callback_helper.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/this_node.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/time.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/timer.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/timer_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/topic.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/transport_hints.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/types.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/wall_timer.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/ros/wall_timer_options.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/rosconsole/macros_generated.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/sensor_msgs/Joy.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/std_msgs/Header.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/std_msgs/Int32.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/xmlrpcpp/XmlRpcDecl.h
cpp_version/CMakeFiles/xbox_controller_local.dir/src/xbox_controller_local.cpp.o: /opt/ros/noetic/include/xmlrpcpp/XmlRpcValue.h

