/usr/bin/c++ -fPIC   -shared -Wl,-soname,libled_gazebo_plugin.so -o /home/<USER>/BeeSwarm/Code/vehicle-computing-board-beeswarm/vswarm_sim_ws/devel/lib/libled_gazebo_plugin.so CMakeFiles/led_gazebo_plugin.dir/src/led_gazebo_plugin.cc.o   -L/usr/lib/x86_64-linux-gnu/gazebo-11/plugins  -Wl,-rpath,/usr/lib/x86_64-linux-gnu/gazebo-11/plugins:/opt/ros/noetic/lib /opt/ros/noetic/lib/libgazebo_ros_api_plugin.so /opt/ros/noetic/lib/libgazebo_ros_paths_plugin.so -ltinyxml /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/libtf.so /opt/ros/noetic/lib/libtf2_ros.so /opt/ros/noetic/lib/libactionlib.so /opt/ros/noetic/lib/libmessage_filters.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libtf2.so /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libdynamic_reconfigure_config_init_mutex.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 -lBulletSoftBody -lBulletDynamics -lBulletCollision -lLinearMath /usr/lib/x86_64-linux-gnu/libSimTKsimbody.so.3.6 /usr/lib/x86_64-linux-gnu/libdart.so.6.9.2 -lgazebo -lgazebo_client -lgazebo_gui -lgazebo_sensors -lgazebo_rendering -lgazebo_physics -lgazebo_ode -lgazebo_transport -lgazebo_msgs -lgazebo_util -lgazebo_common -lgazebo_gimpact -lgazebo_opcode -lgazebo_opende_ou /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_iostreams.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 -lprotobuf -lpthread /usr/lib/x86_64-linux-gnu/libsdformat9.so.9.10.1 -lOgreMain /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 -lOgreTerrain -lOgrePaging /usr/lib/x86_64-linux-gnu/libignition-common3-graphics.so.3.17.0 /usr/lib/x86_64-linux-gnu/libSimTKmath.so.3.6 /usr/lib/x86_64-linux-gnu/libSimTKcommon.so.3.6 -lblas -llapack -lblas -llapack -lrt -lm -ldl /usr/lib/x86_64-linux-gnu/libdart-external-odelcpsolver.so.6.9.2 -lccd -lfcl -lassimp /usr/lib/x86_64-linux-gnu/liboctomap.so.1.9.3 /usr/lib/x86_64-linux-gnu/liboctomath.so.1.9.3 /usr/lib/x86_64-linux-gnu/libboost_atomic.so.1.71.0 /usr/lib/x86_64-linux-gnu/libignition-transport8.so.8.5.0 /usr/lib/x86_64-linux-gnu/libignition-fuel_tools4.so.4.9.1 /usr/lib/x86_64-linux-gnu/libignition-msgs5.so.5.11.0 /usr/lib/x86_64-linux-gnu/libignition-math6.so.6.15.1 /usr/lib/x86_64-linux-gnu/libprotobuf.so -lpthread /usr/lib/x86_64-linux-gnu/libignition-common3.so.3.17.0 -lpthread /usr/lib/x86_64-linux-gnu/libuuid.so -luuid 
