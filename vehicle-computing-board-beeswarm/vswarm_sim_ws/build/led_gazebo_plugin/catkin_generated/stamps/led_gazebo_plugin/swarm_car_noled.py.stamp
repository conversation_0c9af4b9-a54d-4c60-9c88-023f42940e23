#!/usr/bin/env python3

import rospy
from std_msgs.msg import Float32MultiArray

if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("swarm_car_noled")

    # 创建发布者对象
    pub0 = rospy.Publisher("/swarm_car1/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub1 = rospy.Publisher("/swarm_car1/upper_led_ctrl", Float32MultiArray, queue_size=10)
    pub2 = rospy.Publisher("/swarm_car2/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub3 = rospy.Publisher("/swarm_car2/upper_led_ctrl", Float32MultiArray, queue_size=10)
    pub4 = rospy.Publisher("/swarm_car3/lower_led_ctrl", Float32MultiArray, queue_size=10)
    pub5 = rospy.Publisher("/swarm_car3/upper_led_ctrl", Float32MultiArray, queue_size=10)

    # 设置循环频率为1Hz
    rate = rospy.Rate(1)

    # 创建关闭LED的消息
    msg = Float32MultiArray()
    msg.data = [0.0, 0.0, 0.0]  # 关闭LED

    while not rospy.is_shutdown():
        # 发布关闭LED的消息
        pub0.publish(msg)
        pub1.publish(msg)
        pub2.publish(msg)
        pub3.publish(msg)
        pub4.publish(msg)
        pub5.publish(msg)
        
        rate.sleep() 