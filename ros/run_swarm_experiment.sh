#!/bin/bash
# 集群实验启动脚本
# 确保ROS环境正确加载后启动重构版本

echo "🚀 启动集群实验系统..."

# 设置ROS环境
echo "📡 设置ROS环境..."
source /opt/ros/noetic/setup.bash

# 检查ROS环境
echo "🔍 检查ROS环境..."
python3 -c "import rospy; print('✅ ROS环境正常')" 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ ROS环境检查通过"
else
    echo "❌ ROS环境检查失败"
    echo "请确保ROS Noetic已正确安装"
    exit 1
fi

# 启动重构版本
echo "🎯 启动重构版集群实验..."
python3 V6simple_2d_swarm_experiment_refactored.py

echo "🏁 实验结束"
