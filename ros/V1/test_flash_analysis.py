#!/usr/bin/env python3
"""
灯语分析测试脚本
用于分析 /home/<USER>/nnDataset/0920/labels/cam2 中的label文件
检查为什么没有检测到有效的集群指令
"""

import os
import sys
import numpy as np
from collections import Counter, defaultdict
import re

# 添加当前目录到Python路径
sys.path.append('/home/<USER>/Downloads/ros')

try:
    from newan import analyze_flash_patterns_v5_multi_target, map_patterns_to_numbers_multi_target
    print("✅ 成功导入newan模块")
except ImportError as e:
    print(f"❌ 导入newan模块失败: {e}")
    sys.exit(1)

def numerical_sort(value):
    """按照文件名中的数字部分进行排序"""
    numbers = re.findall(r'\d+', value)
    return list(map(int, numbers))

def analyze_label_files(label_path):
    """分析label文件的基本信息"""
    print(f"\n📁 分析路径: {label_path}")

    if not os.path.exists(label_path):
        print(f"❌ 路径不存在: {label_path}")
        return

    # 获取所有label文件
    files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
    files = sorted(files, key=numerical_sort)

    print(f"📄 找到 {len(files)} 个label文件")

    if len(files) == 0:
        print("❌ 没有找到label文件")
        return

    # 分析文件内容
    track_stats = defaultdict(lambda: {'frames': 0, 'detections': 0, 'classes': set()})
    total_detections = 0
    frame_with_detections = 0

    print(f"\n📊 分析前10个文件的内容:")
    for i, file in enumerate(files[:10]):
        file_path = os.path.join(label_path, file)
        frame_number = int(file.split('.')[0])

        with open(file_path, 'r') as f:
            lines = f.readlines()

        if lines:
            frame_with_detections += 1
            print(f"  {file}: {len(lines)} 个检测")

            for line in lines:
                data = line.strip().split()
                if len(data) >= 6:  # track_id, class_id, x, y, w, h
                    track_id = int(data[0])
                    class_id = int(data[1])

                    track_stats[track_id]['frames'] += 1
                    track_stats[track_id]['detections'] += 1
                    track_stats[track_id]['classes'].add(class_id)
                    total_detections += 1

                    if i < 3:  # 只显示前3个文件的详细内容
                        print(f"    TrackID{track_id}, Class{class_id}, 位置({data[2]}, {data[3]})")
        else:
            print(f"  {file}: 空文件")

    print(f"\n📈 统计信息:")
    print(f"  总检测数: {total_detections}")
    print(f"  有检测的帧数: {frame_with_detections}/{len(files)}")
    print(f"  检测到的TrackID数量: {len(track_stats)}")

    print(f"\n🎯 各TrackID统计:")
    for track_id, stats in sorted(track_stats.items()):
        classes_str = ', '.join(map(str, sorted(stats['classes'])))
        zero_count = sum(1 for cls in stats['classes'] if cls == 0)
        non_zero_count = len(stats['classes']) - zero_count
        print(f"  TrackID{track_id}: {stats['detections']}次检测, {stats['frames']}帧, 类别[{classes_str}]")
        print(f"    -> 0类: {zero_count}种, 非0类: {non_zero_count}种")

        # 检查是否有闪烁的可能性（需要同时有0类和非0类）
        if 0 in stats['classes'] and len(stats['classes']) > 1:
            print(f"    ✅ 可能有闪烁模式（同时包含0类和非0类）")
        else:
            print(f"    ❌ 无闪烁可能（只有单一类别或无0类）")

    return track_stats, files

def analyze_ratio_details(label_path, num_frames=40, start_from_frame=1, threshold=0.4):
    """详细分析比例计算过程"""
    print(f"\n📊 详细比例分析 (num_frames={num_frames}, start_from_frame={start_from_frame}, threshold={threshold})")

    files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
    files = sorted(files, key=numerical_sort)

    # 按TrackID分组存储数据
    track_data = defaultdict(lambda: defaultdict(list))

    # 读取文件并提取每帧的每个TrackID的class_id
    for file in files:
        frame_number = int(file.split('.')[0])
        if frame_number < start_from_frame:
            continue

        with open(os.path.join(label_path, file), 'r') as f:
            lines = f.readlines()
            for line in lines:
                data = line.strip().split()
                if len(data) >= 6:  # track_id, class_id, x, y, w, h
                    track_id = int(data[0])
                    class_id = int(data[1])

                    track_data[track_id][frame_number].append({
                        'class_id': class_id
                    })

    # 为每个TrackID分析比例
    for track_id, frame_data in track_data.items():
        print(f"\n🎯 TrackID{track_id} 比例分析:")

        # 获取该TrackID的所有帧范围
        all_frames = sorted(frame_data.keys())
        if len(all_frames) < num_frames:
            print(f"   ❌ 帧数不足: {len(all_frames)} < {num_frames}")
            continue

        ratios_and_patterns = []

        # 分析每个滑动窗口
        for start_frame in range(min(all_frames), max(all_frames) - num_frames + 1):
            zero_count = 0
            non_zero_count = 0
            non_zero_class = None

            # 统计窗口内0类和非0类的数量
            for frame_number in range(start_frame, start_frame + num_frames):
                if frame_number in frame_data:
                    for detection in frame_data[frame_number]:
                        class_id = detection['class_id']
                        if class_id == 0:
                            zero_count += 1
                        else:
                            non_zero_class = class_id
                            non_zero_count += 1

            # 计算比例并生成模式
            if non_zero_class is not None and zero_count > 0:
                ratio = non_zero_count / zero_count

                # 判断模式
                if abs(ratio - 1) <= threshold:  # 接近1:1
                    pattern = f"{non_zero_class}{non_zero_class}00"
                    pattern_type = "1:1比例"
                elif abs(ratio - 2) <= threshold:  # 接近2:1
                    pattern = f"{non_zero_class}{non_zero_class}0"
                    pattern_type = "2:1比例"
                else:
                    pattern = "无闪烁"
                    pattern_type = "其他比例"

                ratios_and_patterns.append({
                    'window': f"{start_frame}-{start_frame + num_frames - 1}",
                    'zero_count': zero_count,
                    'non_zero_count': non_zero_count,
                    'non_zero_class': non_zero_class,
                    'ratio': ratio,
                    'pattern': pattern,
                    'pattern_type': pattern_type
                })

        # 显示前5个窗口的详细信息
        print(f"   📋 前5个滑动窗口的比例计算:")
        for i, info in enumerate(ratios_and_patterns[:5]):
            print(f"     窗口{i+1} [{info['window']}]:")
            print(f"       0类: {info['zero_count']}次, {info['non_zero_class']}类: {info['non_zero_count']}次")
            print(f"       比例: {info['non_zero_count']}/{info['zero_count']} = {info['ratio']:.3f}")
            print(f"       判断: {info['pattern_type']} → 模式: {info['pattern']}")

        # 统计所有模式
        all_patterns = [info['pattern'] for info in ratios_and_patterns]
        pattern_counts = Counter(all_patterns)
        print(f"   📊 所有窗口模式统计: {dict(pattern_counts)}")

        # 显示"无闪烁"窗口的详情
        no_flash_windows = [info for info in ratios_and_patterns if info['pattern'] == '无闪烁']
        if no_flash_windows:
            print(f"   ❌ {len(no_flash_windows)}个'无闪烁'窗口详情:")
            for i, info in enumerate(no_flash_windows[:3]):  # 只显示前3个
                print(f"     无闪烁窗口{i+1} [{info['window']}]:")
                print(f"       0类: {info['zero_count']}次, {info['non_zero_class']}类: {info['non_zero_count']}次")
                print(f"       比例: {info['ratio']:.3f} (不在1±0.4或2±0.4范围内)")

        # 显示最终选择的模式
        most_common_patterns = pattern_counts.most_common(3)
        final_patterns = [pattern for pattern, _ in most_common_patterns]
        print(f"   🏆 最终选择的模式: {final_patterns}")
        print(f"   📈 总滑动窗口数: {len(ratios_and_patterns)}")

def test_flash_analysis(label_path):
    """测试灯语分析"""
    print(f"\n🔆 开始灯语分析测试...")

    # 先进行详细的比例分析
    analyze_ratio_details(label_path)

    # 测试不同参数组合
    test_configs = [
        {'num_frames': 40, 'start_from_frame': 1, 'threshold': 0.4, 'name': '原默认配置'},
        {'num_frames': 36, 'start_from_frame': 7, 'threshold': 0.2, 'name': '理论最优配置'},
        {'num_frames': 54, 'start_from_frame': 7, 'threshold': 0.3, 'name': '稳定优先配置'},
        {'num_frames': 18, 'start_from_frame': 7, 'threshold': 0.15, 'name': '快速响应配置'},
    ]

    for config in test_configs:
        print(f"\n🧪 测试配置: {config['name']}")
        print(f"   参数: num_frames={config['num_frames']}, start_from_frame={config['start_from_frame']}, threshold={config['threshold']}")

        try:
            # 分析闪烁模式
            all_track_patterns = analyze_flash_patterns_v5_multi_target(
                label_path,
                config['num_frames'],
                start_from_frame=config['start_from_frame'],
                threshold=config['threshold']
            )

            # 映射到数字
            track_numbers = map_patterns_to_numbers_multi_target(all_track_patterns)

            print(f"   结果: 检测到 {len(all_track_patterns)} 个TrackID的闪烁模式")

            # 显示详细结果
            for track_id, patterns in all_track_patterns.items():
                numbers = track_numbers.get(track_id, [])
                print(f"     TrackID{track_id}: {patterns} → {numbers}")

                # 检查是否有有效指令
                valid_commands = []
                for pattern, number in zip(patterns, numbers):
                    if pattern != "无闪烁" and number != -1 and 1 <= number <= 8:
                        valid_commands.append(f"指令{number}")

                if valid_commands:
                    print(f"       ✅ 有效指令: {', '.join(valid_commands)}")
                else:
                    print(f"       ❌ 无有效指令")

        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()

def analyze_class_distribution(label_path):
    """分析类别分布，查看闪烁模式"""
    print(f"\n📊 分析类别分布和闪烁模式...")
    
    files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
    files = sorted(files, key=numerical_sort)
    
    # 按TrackID分组分析类别变化
    track_class_sequence = defaultdict(list)  # track_id -> [(frame, class_id), ...]
    
    for file in files:
        frame_number = int(file.split('.')[0])
        file_path = os.path.join(label_path, file)
        
        with open(file_path, 'r') as f:
            lines = f.readlines()
            
        for line in lines:
            data = line.strip().split()
            if len(data) >= 6:
                track_id = int(data[0])
                class_id = int(data[1])
                track_class_sequence[track_id].append((frame_number, class_id))
    
    # 分析每个TrackID的类别变化模式
    for track_id, sequence in track_class_sequence.items():
        sequence.sort()  # 按帧号排序
        
        print(f"\n🎯 TrackID{track_id} 类别序列分析:")
        print(f"   总检测次数: {len(sequence)}")
        
        # 统计类别分布
        class_counts = Counter([class_id for _, class_id in sequence])
        print(f"   类别分布: {dict(class_counts)}")
        
        # 显示前20个检测的类别序列
        if len(sequence) > 0:
            class_sequence = [class_id for _, class_id in sequence[:20]]
            print(f"   前20个类别序列: {class_sequence}")
            
            # 查找连续的类别模式
            patterns = []
            for i in range(len(class_sequence) - 3):
                pattern = ''.join(map(str, class_sequence[i:i+4]))
                patterns.append(pattern)
            
            if patterns:
                pattern_counts = Counter(patterns)
                common_patterns = pattern_counts.most_common(5)
                print(f"   常见4位模式: {common_patterns}")

def main():
    """主函数"""
    print("🔍 灯语分析诊断工具")
    print("=" * 60)
    
    label_path = "/home/<USER>/nnDataset/0920/labels/cam2"
    
    # 1. 分析label文件基本信息
    track_stats, files = analyze_label_files(label_path)
    
    if not track_stats:
        print("❌ 没有有效的检测数据，无法进行灯语分析")
        return
    
    # 2. 分析类别分布和序列
    analyze_class_distribution(label_path)
    
    # 3. 测试灯语分析
    test_flash_analysis(label_path)
    
    print(f"\n💡 诊断建议:")
    print(f"1. 检查类别序列是否包含预期的闪烁模式（如220, 330, 110等）")
    print(f"2. 如果只有单一类别，可能目标没有进行闪烁")
    print(f"3. 如果类别变化不规律，可能需要调整threshold参数")
    print(f"4. 如果帧数不足，可能需要减少num_frames参数")

if __name__ == "__main__":
    main()
