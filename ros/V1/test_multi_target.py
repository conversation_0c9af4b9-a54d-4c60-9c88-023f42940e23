#!/usr/bin/env python3
"""
多目标功能测试脚本
验证newan.py和cal_vec.py的多目标支持
"""

import os
import sys
import numpy as np
from newan import analyze_flash_patterns_v5_multi_target, map_patterns_to_numbers_multi_target
from cal_vec import find_multi_target_distances_planar, create_camera_matrix

def test_multi_target_analysis():
    """测试多目标分析功能"""
    print("🧪 多目标功能测试")
    print("=" * 50)
    
    # 测试路径
    base_path = '/home/<USER>/nnDataset/0920/labels'
    
    # 检查路径是否存在
    if not os.path.exists(base_path):
        print(f"❌ 基础路径不存在: {base_path}")
        return
    
    # 查找可用的摄像头文件夹
    available_cameras = []
    for item in os.listdir(base_path):
        item_path = os.path.join(base_path, item)
        if os.path.isdir(item_path) and item.startswith('cam'):
            available_cameras.append(item)
    
    if not available_cameras:
        print("❌ 未找到摄像头文件夹")
        return
    
    print(f"📹 找到摄像头: {available_cameras}")
    
    # 测试每个摄像头
    for cam_name in available_cameras:
        label_path = os.path.join(base_path, cam_name)
        print(f"\n🔍 测试摄像头: {cam_name}")
        print(f"   路径: {label_path}")
        
        # 检查标签文件
        txt_files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
        print(f"   标签文件数量: {len(txt_files)}")
        
        if len(txt_files) == 0:
            print("   ⚠️ 无标签文件，跳过")
            continue
        
        # 测试多目标灯语分析
        try:
            print("   🔆 测试多目标灯语分析...")
            all_track_patterns = analyze_flash_patterns_v5_multi_target(label_path, 40, start_from_frame=1, threshold=0.4)
            track_numbers = map_patterns_to_numbers_multi_target(all_track_patterns)
            
            print(f"   ✅ 灯语分析完成，检测到 {len(all_track_patterns)} 个TrackID")
            
            for track_id, patterns in all_track_patterns.items():
                if track_id in track_numbers:
                    numbers = track_numbers[track_id]
                    print(f"     TrackID {track_id}: {patterns} → {numbers}")
                else:
                    print(f"     TrackID {track_id}: {patterns} → 无映射")
                    
        except Exception as e:
            print(f"   ❌ 灯语分析失败: {e}")
        
        # 测试多目标空间分析
        try:
            print("   📐 测试多目标空间分析...")
            camera_matrix = create_camera_matrix()
            track_results = find_multi_target_distances_planar(label_path, real_width=0.31, camera_matrix=camera_matrix, debug=False)
            
            print(f"   ✅ 空间分析完成，检测到 {len(track_results)} 个有效TrackID")
            
            for track_id, (position_vector, distance, theta, azimuth, elevation) in track_results.items():
                print(f"     TrackID {track_id}: 距离={distance:.2f}m, 方位角={azimuth:.1f}°")
                print(f"       位置向量: [{position_vector[0]:.2f}, {position_vector[1]:.2f}, {position_vector[2]:.2f}]")
                
        except Exception as e:
            print(f"   ❌ 空间分析失败: {e}")
    
    print("\n" + "=" * 50)
    print("✅ 多目标功能测试完成")

def test_label_file_format():
    """测试标签文件格式"""
    print("\n📋 标签文件格式测试")
    print("=" * 30)
    
    base_path = '/home/<USER>/nnDataset/0920/labels'
    
    # 查找第一个可用的标签文件
    for cam_name in ['cam0', 'cam1', 'cam2', 'cam3']:
        label_path = os.path.join(base_path, cam_name)
        if os.path.exists(label_path):
            txt_files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
            if txt_files:
                # 读取第一个文件
                first_file = os.path.join(label_path, txt_files[0])
                print(f"📄 分析文件: {first_file}")
                
                with open(first_file, 'r') as f:
                    lines = f.readlines()
                    if lines:
                        first_line = lines[0].strip()
                        data = first_line.split()
                        print(f"   第一行数据: {first_line}")
                        print(f"   数据字段数: {len(data)}")
                        
                        if len(data) >= 7:
                            print(f"   ✅ 格式正确 (TrackID, ClassID, Conf, X, Y, W, H)")
                            print(f"   TrackID: {data[0]}")
                            print(f"   ClassID: {data[1]}")
                            print(f"   Confidence: {data[2]}")
                            print(f"   X, Y, W, H: {data[3:7]}")
                        else:
                            print(f"   ❌ 格式不正确，期望至少7个字段")
                        
                        # 统计所有行的TrackID
                        track_ids = set()
                        class_ids = set()
                        for line in lines:
                            data = line.strip().split()
                            if len(data) >= 2:
                                track_ids.add(data[0])
                                class_ids.add(data[1])
                        
                        print(f"   文件中的TrackID: {sorted(track_ids)}")
                        print(f"   文件中的ClassID: {sorted(class_ids)}")
                        break
                break
    else:
        print("❌ 未找到可用的标签文件")

if __name__ == '__main__':
    test_label_file_format()
    test_multi_target_analysis() 