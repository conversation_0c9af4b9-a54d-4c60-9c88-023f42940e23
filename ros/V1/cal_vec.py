import os
import numpy as np
import math
from collections import defaultdict


def create_camera_matrix(f_x=381.36246688113556, f_y=None, c_x=320.5, c_y=180.5):
    """创建相机内参矩阵"""
    if f_y is None:
        f_y = f_x
    return np.array([
        [f_x, 0, c_x],
        [0, f_y, c_y],
        [0, 0, 1]
    ])


def calculate_azimuth_planar(x_pixel, camera_params, debug=False):
    """
    基于平面几何的方位角计算 - 针对同平面多机器人场景优化
    
    核心思想：
    1. 所有目标在同一水平面，摄像头固定高度
    2. 图像Y坐标基本固定，主要变化是X坐标  
    3. 水平方位角直接由X轴偏移决定
    
    :param x_pixel: 目标在图像中的X坐标
    :param camera_params: 相机参数字典 {'fx': fx, 'cx': cx}
    :param debug: 是否输出调试信息
    :return: 方位角（度，相对于相机光轴）
    """
    
    fx = camera_params.get('fx', 381.36246688113556)
    cx = camera_params.get('cx', 320.5)
    
    # 🔧 核心算法：基于X轴偏移的方位角计算
    # 水平偏移角 = atan((像素偏移) / 焦距)
    pixel_offset = x_pixel - cx  # 相对于图像中心的X偏移
    angle_rad = math.atan(pixel_offset / fx)  # 转换为角度（弧度）
    angle_deg = math.degrees(angle_rad)  # 转换为度
    
    # 转换到相机坐标系定义：
    # 0° = 正前方，正值 = 右侧，负值 = 左侧
    # 标准化到[0, 360)范围
    if angle_deg < 0:
        azimuth = 360 + angle_deg  # 负角度转换为270-360度范围
    else:
        azimuth = angle_deg
    
    if debug:
        print(f"🔍 平面几何方位角计算:")
        print(f"   X像素坐标: {x_pixel:.1f}")
        print(f"   图像中心CX: {cx}")
        print(f"   像素偏移: {pixel_offset:.1f}")
        print(f"   焦距FX: {fx:.1f}")
        print(f"   计算角度: atan({pixel_offset:.1f}/{fx:.1f}) = {angle_deg:.2f}°")
        print(f"   标准化方位角: {azimuth:.2f}°")
        
        # 解释方向
        if -10 <= angle_deg <= 10:
            direction = "正前方"
        elif angle_deg > 10:
            direction = "右前方"
        else:
            direction = "左前方"
        print(f"   目标方向: {direction}")
    
    return azimuth


def calculate_distance_planar(detected_width, real_width, fx, debug=False):
    """
    基于检测框宽度的距离计算（简化版本）
    
    基本原理：distance = (real_width × fx) / pixel_width
    这个公式在小角度近似下是准确的
    
    :param detected_width: YOLO检测框的像素宽度
    :param real_width: 目标的真实宽度（米）
    :param fx: 相机焦距
    :param debug: 是否输出调试信息
    :return: 距离（米）
    """
    
    distance = (real_width * fx) / detected_width
    
    if debug:
        print(f"🔍 平面几何距离计算:")
        print(f"   真实宽度: {real_width}m")
        print(f"   像素宽度: {detected_width:.1f}px")
        print(f"   焦距: {fx:.1f}px")
        print(f"   计算距离: ({real_width} × {fx:.1f}) / {detected_width:.1f} = {distance:.2f}m")
    
    return distance


def calculate_position_vector_planar(detected_width, x_box, y_box, camera_matrix_inv, real_width, debug=False):
    """
    基于平面几何的位置向量计算 - 专为同平面多机器人优化
    
    优化特点：
    1. 简化的方位角计算，直接基于X轴偏移
    2. 简化的距离计算，基于YOLO框宽度  
    3. 针对水平面场景的position_vector计算
    
    :param detected_width: 检测框的宽度（像素）
    :param x_box: 检测框中心的X坐标（像素）
    :param y_box: 检测框中心的Y坐标（像素）
    :param camera_matrix_inv: 相机矩阵的逆（为了兼容性保留）
    :param real_width: 目标的实际宽度（米）
    :param debug: 是否输出调试信息
    :return: (position_vector, distance, theta, azimuth, elevation)
    """
    
    # 提取相机参数
    fx = 1.0 / camera_matrix_inv[0][0]  # 从逆矩阵恢复fx
    cx = -camera_matrix_inv[0][2] * fx   # 从逆矩阵恢复cx
    
    camera_params = {'fx': fx, 'cx': cx}
    
    if debug:
        print(f"\n{'='*50}")
        print(f"🚗 平面几何位置计算 - 检测框({x_box:.1f}, {y_box:.1f})")
        print(f"{'='*50}")
    
    # 🔧 步骤1：计算方位角（核心优化）
    azimuth = calculate_azimuth_planar(x_box, camera_params, debug)
    
    # 🔧 步骤2：计算距离（简化算法）
    distance = calculate_distance_planar(detected_width, real_width, fx, debug)
    
    # 🔧 步骤3：计算position_vector（基于极坐标）
    # 在水平面上，根据距离和方位角计算相对位置
    azimuth_rad = math.radians(azimuth)
    
    # 相机坐标系：X右，Y下，Z前
    # 目标相对位置：
    pos_x = distance * math.sin(azimuth_rad)  # 水平右方距离
    pos_y = 0.0  # 垂直位置差异忽略（同平面假设）
    pos_z = distance * math.cos(azimuth_rad)  # 前方距离
    
    position_vector = np.array([pos_x, pos_y, pos_z])
    
    # theta和elevation为兼容性保留（2D场景下不重要）
    theta = 0.0  # 角度展开，2D场景下简化
    elevation = 0.0  # 俯仰角，同平面场景下为0
    
    if debug:
        print(f"🎯 最终结果:")
        print(f"   方位角: {azimuth:.2f}° ({'右前方' if 0 < azimuth < 90 else '左前方' if 270 < azimuth < 360 else '前方'})")
        print(f"   距离: {distance:.2f}m") 
        print(f"   相机坐标系位置: [{pos_x:.2f}, {pos_y:.2f}, {pos_z:.2f}]")
        print(f"   水平偏移: {pos_x:.2f}m ({'右' if pos_x > 0 else '左'})")
        print(f"   前方距离: {pos_z:.2f}m")
        print(f"{'='*50}\n")
    
    return position_vector, distance, theta, azimuth, elevation


def find_first_five_non_zero_distances_planar(folder_path, real_width=0.31, camera_matrix=None, debug=False):
    """
    使用平面几何算法的版本 - 单一目标版本（向后兼容）
    """
    if camera_matrix is None:
        camera_matrix = create_camera_matrix()
    camera_matrix_inv = np.linalg.inv(camera_matrix)

    position_vectors = []
    distances = []
    thetas = []
    azimuths = []
    elevations = []
    
    count = 0
    for filename in sorted(os.listdir(folder_path)):  # 排序确保一致性
        if filename.endswith('.txt'):
            file_path = os.path.join(folder_path, filename)
            with open(file_path, 'r') as file:
                lines = file.readlines()
                for line in lines:
                    data = line.strip().split()
                    if len(data) >= 5:
                        class_id = int(data[0])
                        if class_id != 110:  # 忽略特定类别
                            detected_width = float(data[3]) * 640  # 转换为像素宽度
                            x_box = float(data[1]) * 640  # 转换为像素坐标
                            y_box = float(data[2]) * 360  # 转换为像素坐标

                            # 🔧 使用新的平面几何算法
                            position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                                detected_width, x_box, y_box, camera_matrix_inv, real_width, debug)
                            
                            position_vectors.append(position_vector)
                            distances.append(distance)
                            thetas.append(theta)
                            azimuths.append(azimuth)
                            elevations.append(elevation)

                            count += 1
                            if count == 5:
                                avg_position_vector = np.mean(position_vectors, axis=0)
                                avg_distance = np.mean(distances)
                                avg_theta = np.mean(thetas)
                                avg_azimuth = np.mean(azimuths)
                                avg_elevation = np.mean(elevations)

                                return avg_position_vector, avg_distance, avg_theta, avg_azimuth, avg_elevation

    return None, None, None, None, None


def find_multi_target_distances_planar(folder_path, real_width=0.31, camera_matrix=None, debug=False):
    """
    使用平面几何算法的多目标版本 - 按TrackID分组处理
    
    :param folder_path: 标签文件夹路径
    :param real_width: 目标真实宽度
    :param camera_matrix: 相机矩阵
    :param debug: 是否输出调试信息
    :return: 字典 {track_id: (position_vector, distance, theta, azimuth, elevation)}
    """
    if camera_matrix is None:
        camera_matrix = create_camera_matrix()
    camera_matrix_inv = np.linalg.inv(camera_matrix)

    # 按TrackID分组存储数据
    track_data = defaultdict(list)  # track_id -> [(position_vector, distance, theta, azimuth, elevation), ...]
    
    for filename in sorted(os.listdir(folder_path)):
        if filename.endswith('.txt'):
            file_path = os.path.join(folder_path, filename)
            with open(file_path, 'r') as file:
                lines = file.readlines()
                for line in lines:
                    data = line.strip().split()
                    if len(data) >= 7:  # track_id, class_id, conf, x, y, w, h
                        track_id = int(data[0])
                        class_id = int(data[1])
                        confidence = float(data[2])
                        x_center, y_center, width, height = map(float, data[3:7])
                        
                        if class_id != 110:  # 忽略特定类别
                            # 检查坐标格式：如果坐标值大于1，说明是像素坐标，否则是归一化坐标
                            if x_center > 1.0 or y_center > 1.0 or width > 1.0 or height > 1.0:
                                # 已经是像素坐标，直接使用
                                detected_width = width
                                x_box = x_center
                                y_box = y_center
                            else:
                                # 归一化坐标，转换为像素坐标
                                detected_width = width * 640
                                x_box = x_center * 640
                                y_box = y_center * 360

                            # 使用平面几何算法计算空间信息
                            position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
                                detected_width, x_box, y_box, camera_matrix_inv, real_width, debug)
                            
                            track_data[track_id].append({
                                'position_vector': position_vector,
                                'distance': distance,
                                'theta': theta,
                                'azimuth': azimuth,
                                'elevation': elevation,
                                'confidence': confidence,
                                'class_id': class_id
                            })

    # 为每个TrackID计算平均空间信息
    track_results = {}
    for track_id, detections in track_data.items():
        if len(detections) >= 3:  # 至少需要3个检测点
            # 按置信度排序，取前5个最高置信度的检测
            sorted_detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)[:5]
            
            # 计算平均值
            avg_position_vector = np.mean([d['position_vector'] for d in sorted_detections], axis=0)
            avg_distance = np.mean([d['distance'] for d in sorted_detections])
            avg_theta = np.mean([d['theta'] for d in sorted_detections])
            avg_azimuth = np.mean([d['azimuth'] for d in sorted_detections])
            avg_elevation = np.mean([d['elevation'] for d in sorted_detections])
            
            track_results[track_id] = (avg_position_vector, avg_distance, avg_theta, avg_azimuth, avg_elevation)
            
            if debug:
                print(f"🎯 TrackID {track_id}: 距离={avg_distance:.2f}m, 方位角={avg_azimuth:.1f}°")

    return track_results


# # 🧪 主程序：验证和对比
# if __name__ == "__main__":
#     print("🚗 平面几何方位角计算算法 v2.0")
#     print("=" * 50)
    
#     # 算法验证
#     validate_planar_geometry()
    
#     # 与用户数据对比
#     compare_with_original_data()
    
#     print("\n💡 关键改进:")
#     print("1. 🎯 直接基于X轴偏移计算方位角: atan((x-cx)/fx)")
#     print("2. 🚀 简化距离计算: real_width × fx / pixel_width")
#     print("3. 🔧 针对同平面场景优化，消除Y轴干扰")
#     print("4. 📊 预期解决方位角'量化'现象")
#     print("\n🔄 建议替换simple_2d_swarm_experiment.py中的calculate_position_vector调用")