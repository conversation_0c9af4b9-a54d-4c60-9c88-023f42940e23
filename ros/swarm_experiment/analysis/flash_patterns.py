#!/usr/bin/env python3
"""
闪烁模式分析模块
从原 newan.py 重构而来，提供更规范的命名和结构

功能：
- 分析目标的闪烁模式
- 基于0类和非0类的比例关系生成闪烁模式
- 支持多目标TrackID分析
- 将闪烁模式映射到数字指令
"""

import os
import re
import time
from collections import Counter, defaultdict

def numerical_sort(value):
    """
    按照文件名中的数字部分进行排序的函数。
    """
    numbers = re.findall(r'\d+', value)
    return list(map(int, numbers))

def analyze_flash_patterns_v5_multi_target(label_folder, num_frames, start_from_frame=25, threshold=0.2):
    """
    分析从指定帧开始的每个TrackID对象的闪烁模式，基于非0类与0类的比例关系生成闪烁模式。
    支持多目标情况，按TrackID分组分析。
    """
    files = sorted(os.listdir(label_folder), key=numerical_sort)
    
    # 按TrackID分组存储数据
    track_data = defaultdict(lambda: defaultdict(list))  # track_id -> frame -> [(class_id, x, y, w, h), ...]

    # 读取文件并提取每帧的每个TrackID的class_id
    for file in files:
        frame_number = int(file.split("_")[-1].split(".")[0])
        if frame_number < start_from_frame:
            continue

        with open(os.path.join(label_folder, file), 'r') as f:
            lines = f.readlines()
            for line in lines:
                data = line.strip().split()
                if len(data) >= 6:  # track_id, class_id, x, y, w, h
                    track_id = int(data[0])
                    class_id = int(data[1])
                    x, y, w, h = map(float, data[2:6])
                    
                    track_data[track_id][frame_number].append({
                        'class_id': class_id,
                        'x': x, 'y': y, 'w': w, 'h': h
                    })

    # 为每个TrackID分析闪烁模式
    all_track_patterns = {}
    
    for track_id, frame_data in track_data.items():
        flash_patterns = []
        
        # 获取该TrackID的所有帧范围
        all_frames = sorted(frame_data.keys())
        if len(all_frames) < num_frames:
            all_track_patterns[track_id] = ["无闪烁"]
            continue
        
        # 分析每个滑动窗口
        for start_frame in range(min(all_frames), max(all_frames) - num_frames + 1):
            zero_count = 0
            non_zero_count = 0
            non_zero_class = None

            # 统计窗口内0类和非0类的数量
            for frame_number in range(start_frame, start_frame + num_frames):
                if frame_number in frame_data:
                    for detection in frame_data[frame_number]:
                        class_id = detection['class_id']
                        if class_id == 0:
                            zero_count += 1
                        else:
                            non_zero_class = class_id
                            non_zero_count += 1

            # 如果存在非0类，则计算比例并生成闪烁模式
            if non_zero_class is not None and zero_count > 0:
                ratio = non_zero_count / zero_count
                # 判断是否接近1:1 或 2:1
                if abs(ratio - 1) <= threshold:  # 接近1:1
                    flash_patterns.append(f"{non_zero_class}{non_zero_class}00")
                elif abs(ratio - 2) <= threshold:  # 接近2:1
                    flash_patterns.append(f"{non_zero_class}{non_zero_class}0")
                else:
                    flash_patterns.append("无闪烁")
            else:
                flash_patterns.append("无闪烁")

        # 计算闪烁模式的众数，只返回单一最优模式
        if flash_patterns:
            pattern_counts = Counter(flash_patterns)
            
            # 获取最常见的模式
            most_common_pattern, most_common_count = pattern_counts.most_common(1)[0]
            
            # 优先选择有效的闪烁模式（非"无闪烁"）
            valid_patterns = [(pattern, count) for pattern, count in pattern_counts.items() 
                            if pattern != "无闪烁"]
            
            if valid_patterns:
                # 如果有有效的闪烁模式，选择最常见的有效模式
                best_valid_pattern = max(valid_patterns, key=lambda x: x[1])[0]
                all_track_patterns[track_id] = [best_valid_pattern]
            else:
                # 如果只有"无闪烁"模式
                all_track_patterns[track_id] = [most_common_pattern]
        else:
            all_track_patterns[track_id] = ["无闪烁"]  # 如果没有检测到闪烁模式，返回默认模式

    return all_track_patterns

def map_patterns_to_numbers_multi_target(all_track_patterns):
    """
    将多目标的闪烁模式映射到对应的数字。
    """
    pattern_mapping = {
        '220': 1,    # approach (靠近)
        '330': 2,    # follow (跟随)
        '110': 3,    # avoid (避让)
        '550': 4,    # circle (环绕)
        '440': 5,    # stop (停止)
        '2200': 6,   # align (对齐)
        '3300': 7,   # retreat (后退)
        '1100': 8,   # parallel (平行)
        '5500': 9,
        '4400': 10
    }
    
    result = {}
    for track_id, patterns in all_track_patterns.items():
        result[track_id] = [pattern_mapping.get(pattern, -1) for pattern in patterns]
    
    return result

# 保持向后兼容的单目标版本
def analyze_flash_patterns_v5(label_folder, num_frames, start_from_frame=25, threshold=0.2):
    """
    分析从指定帧开始的每个对象的闪烁模式，基于非0类与0类的比例关系生成闪烁模式。
    单目标版本，保持向后兼容。
    """
    files = sorted(os.listdir(label_folder), key=numerical_sort)
    label_data = {}

    # 读取文件并提取每帧的class_id
    for file in files:
        frame_number = int(file.split("_")[-1].split(".")[0])
        if frame_number < start_from_frame:
            continue

        with open(os.path.join(label_folder, file), 'r') as f:
            lines = f.readlines()
            # 获取文件中的 class_id，假设每个文件中只包含一个类别
            class_ids = [int(line.strip().split()[0]) for line in lines]
            label_data[frame_number] = class_ids

    flash_patterns = []

    for start_frame in range(start_from_frame, max(label_data.keys()) - num_frames + 1):
        zero_count = 0
        non_zero_count = 0
        non_zero_class = None

        # 统计48帧中0类和非0类的数量
        for frame_number in range(start_frame, start_frame + num_frames):
            frame_class_ids = label_data[frame_number]
            for class_id in frame_class_ids:
                if class_id == 0:
                    zero_count += 1
                else:
                    non_zero_class = class_id
                    non_zero_count += 1

        # 如果存在非0类，则计算比例并生成闪烁模式
        if non_zero_class is not None:
            ratio = non_zero_count / zero_count
            # 判断是否接近1:1 或 2:1
            if abs(ratio - 1) <= threshold:  # 接近1:1
                flash_patterns.append(f"{non_zero_class}{non_zero_class}00")
            elif abs(ratio - 2) <= threshold:  # 接近2:1
                flash_patterns.append(f"{non_zero_class}{non_zero_class}0")
            else:
                flash_patterns.append("无闪烁")

    # 计算闪烁模式的众数
    if flash_patterns:
        pattern_counts = Counter(flash_patterns)
        most_common_patterns = pattern_counts.most_common(3)  # 获取最多的前三个模式
        final_patterns = [pattern for pattern, _ in most_common_patterns]

        return final_patterns
    else:
        return ["无闪烁"]  # 如果没有检测到闪烁模式，返回默认模式

def map_patterns_to_numbers(patterns):
    """
    将闪烁模式映射到对应的数字。
    单目标版本，保持向后兼容。
    """
    pattern_mapping = {
        '220': 1,
        '330': 2,
        '110': 3,
        '550': 4,
        '440': 5,
        '2200': 6,
        '3300': 7,
        '1100': 8,
        '5500': 9,
        '4400': 10
    }
    return [pattern_mapping.get(pattern, -1) for pattern in patterns]  # -1表示模式未找到
