#!/usr/bin/env python3
"""
配置管理模块
管理所有实验参数和配置
"""

import socket
import numpy as np

class SwarmConfig:
    """集群实验配置类"""
    
    def __init__(self):
        self.node_name = socket.gethostname()
        
        # 简化的2D集群指令
        self.SWARM_COMMANDS = {
            1: "approach",        # 靠近
            2: "follow",          # 跟随
            3: "avoid",           # 避让
            4: "circle",          # 环绕
            5: "stop",            # 停止
            6: "align",           # 对齐
            7: "retreat",         # 后退
            8: "parallel"         # 平行
        }
        
        # 2D空间参数
        self.spatial_params = {
            'target_distance': 2.0,    # 目标距离(米) - approach的目标
            'safe_distance': 1.0,      # 最小安全距离(米) - 太近时后退
            'follow_distance': 2.5,    # 跟随距离(米)
            'max_distance': 10.0,      # 最大检测距离(米)
            'angle_tolerance': 30.0,   # 角度容忍度(度)
            'approach_speed': 0.5,     # 靠近速度
            'retreat_speed': 0.3,      # 后退速度
            'turn_speed': 0.4          # 转向速度
        }
        
        # 摄像头朝向配置
        # 坐标系：0°=+Y轴，90°=+X轴，180°=-Y轴，270°=-X轴
        self.camera_orientations = {
            'cam0': 270,    # -X轴方向 (linear.x < 0)
            'cam1': 180,    # -Y轴方向 (linear.y < 0)
            'cam2': 90,     # +X轴方向 (linear.x > 0)
            'cam3': 0       # +Y轴方向 (linear.y > 0)
        }
        
        # 实验配置
        self.config = {
            'detection_duration': 5,
            'cameras': ['/cam0/image_raw', '/cam1/image_raw', '/cam2/image_raw', '/cam3/image_raw'],
            # 'cameras': [ '/cam2/image_raw', '/cam3/image_raw'],
            'base_path': '/home/<USER>/nnDataset/0920/labels',
            'real_width': 0.31,
            # 智能早停配置
            'early_stop_enabled': True,          # 是否启用智能早停
            'early_stop_check_interval': 1.0,    # 检查间隔(秒)
            'early_stop_no_target_duration': 2.0, # 无目标持续时间阈值(秒)
            'min_detection_duration': 1.0,       # 最小检测时间(秒)
            # 灯语分析配置（理论最优参数）
            'flash_num_frames': 36,              # 分析窗口大小(帧)
            'flash_start_frame': 7,              # 起始帧
            'flash_threshold': 0.2               # 比例容忍度
        }
        
        # ROS话题配置
        self.ros_topics = {
            'velocity': '/cmd_vel',
            'led_command': '/led_command'
        }
        
        # 相机内参矩阵
        self.camera_matrix_inv = None
        self.setup_camera_matrix()
        
    def get_direction_description(self, angle):
        """根据角度返回方向描述"""
        angle = angle % 360
        if angle < 22.5 or angle >= 337.5:
            return "北方"
        elif angle < 67.5:
            return "东北方"
        elif angle < 112.5:
            return "东方"
        elif angle < 157.5:
            return "东南方"
        elif angle < 202.5:
            return "南方"
        elif angle < 247.5:
            return "西南方"
        elif angle < 292.5:
            return "西方"
        else:
            return "西北方"
    
    def update_config(self, key, value):
        """更新配置参数"""
        if key in self.config:
            self.config[key] = value
            return True
        return False
    
    def update_spatial_param(self, key, value):
        """更新空间参数"""
        if key in self.spatial_params:
            self.spatial_params[key] = value
            return True
        return False

    def setup_camera_matrix(self):
        """设置相机内参矩阵"""
        try:
            from ..utils.camera_utils import create_camera_matrix
            # 按照原版本的方式设置相机矩阵
            camera_matrix = create_camera_matrix()
            self.camera_matrix_inv = np.linalg.inv(camera_matrix)
            print("✅ 相机内参矩阵设置完成")
        except Exception as e:
            print(f"⚠️ 相机内参矩阵设置失败: {e}")
            # 设置一个默认值避免后续计算出错
            self.camera_matrix_inv = None
