#!/usr/bin/env python3
"""
摄像头检测模块
负责单个摄像头的目标检测和跟踪
"""

import rospy
import time
import os
import shutil
from collections import defaultdict
from cv_bridge import CvBridge
from ultralytics import YOLO
from sensor_msgs.msg import Image

class CameraDetector:
    """单摄像头检测器"""
    
    def __init__(self, config):
        self.config = config
        self.bridge = CvBridge()
        # 不在初始化时创建模型，避免多线程共享问题
        
    def detect_camera_2d(self, camera_topic):
        """2D空间检测（检测期间只缓存，检测结束后统一空间分析）- 支持智能早停"""
        cam_name = camera_topic.split('/')[-2]
        label_path = f"{self.config.config['base_path']}/{cam_name}"
        
        if os.path.exists(label_path):
            shutil.rmtree(label_path)
        os.makedirs(label_path, exist_ok=True)
        
        rospy.loginfo(f"🎥 {cam_name}: 开始2D检测...")

        # 为每个摄像头创建独立的YOLO模型实例，避免线程冲突
        model = YOLO('/home/<USER>/1206Cars-11/weights/best.engine')
        frame_count = 0
        start_time = time.time()

        # 用于批量缓存label内容和YOLO检测结果
        all_labels = []  # [(frame_count, [line1, line2, ...]), ...]
        all_detections = []  # [(frame_count, [tuple(track_id, class_id, x_center, y_center, width, height)])]
        
        # 智能早停相关变量
        last_target_time = start_time  # 最后一次检测到目标的时间
        detection_stopped = False      # 检测是否已停止
        
        def image_callback(msg):
            nonlocal frame_count, start_time, last_target_time, detection_stopped
            
            current_time = time.time()
            
            # 检查是否超过最大检测时间
            if current_time - start_time > self.config.config['detection_duration']:
                return
                
            # 检查智能早停条件
            if self.config.config.get('early_stop_enabled', False) and not detection_stopped:
                # 确保至少检测了最小时间
                if current_time - start_time >= self.config.config.get('min_detection_duration', 1.0):
                    # 检查是否长时间无目标
                    no_target_duration = current_time - last_target_time
                    if no_target_duration >= self.config.config.get('early_stop_no_target_duration', 2.0):
                        detection_stopped = True
                        rospy.loginfo(f"⏹️ {cam_name}: 智能早停 - {no_target_duration:.1f}s无目标检测")
                        return
            
            cv_image = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
            results = model.track(cv_image, imgsz=(640, 384), stream=True)
            
            # 每帧只处理一次，合并所有结果
            label_lines = []
            detections = []
            has_valid_target = False  # 标记是否有有效目标
            
            for result in results:
                if result.boxes is not None and len(result.boxes) > 0:
                    # 批量处理所有boxes，减少GPU-CPU转换次数
                    boxes_data = result.boxes.xywh.cpu().numpy()  # 一次性转换所有boxes
                    cls_data = result.boxes.cls.cpu().numpy() if result.boxes.cls is not None else None
                    id_data = result.boxes.id.cpu().numpy() if result.boxes.id is not None else None

                    for i, box_xywh in enumerate(boxes_data):
                        cls = int(cls_data[i]) if cls_data is not None else -1
                        track_id = int(id_data[i]) if id_data is not None else -1
                        x_center, y_center, width, height = box_xywh

                        # 写入label行
                        label_lines.append(f"{track_id} {cls} {x_center:.2f} {y_center:.2f} {width:.2f} {height:.2f}\n")

                        # 简化检测结果存储
                        detections.append((track_id, cls, x_center, y_center, width, height))
                        
                        # 检查是否为有效目标（非110类别）
                        if cls != 110:
                            has_valid_target = True

            # 更新最后检测到目标的时间
            if has_valid_target:
                last_target_time = current_time

            # 每帧只添加一次结果
            all_labels.append((frame_count, label_lines))
            all_detections.append((frame_count, detections))
            frame_count += 1
        
        sub = rospy.Subscriber(camera_topic, Image, image_callback)
        
        # 动态等待：要么达到最大时间，要么智能早停
        while not rospy.is_shutdown():
            current_time = time.time()
            if current_time - start_time > self.config.config['detection_duration']:
                break
            if detection_stopped:
                break
            time.sleep(0.1)  # 短暂休眠避免CPU占用过高
            
        sub.unregister()
        
        # 检测结束后批量写入label
        try:
            for frame_count, label_lines in all_labels:
                with open(f"{label_path}/{frame_count}.txt", 'w') as f:
                    f.writelines(label_lines)
            
            # 计算实际检测时间和统计信息
            actual_duration = time.time() - start_time
            
            # 统计信息
            total_detections = sum(len(detections) for _, detections in all_detections)
            valid_detections = sum(1 for _, detections in all_detections 
                                 for det in detections if det[1] != 110)  # det[1]是cls
            frames_with_targets = sum(1 for _, detections in all_detections 
                                    if any(det[1] != 110 for det in detections))
            
            # 注释掉详细的检测完成日志
            # status_msg = f"✅ {cam_name}: 2D检测完成，{frame_count}帧"
            # if detection_stopped:
            #     status_msg += f"（智能早停 {actual_duration:.1f}s）"
            # else:
            #     status_msg += f"（完整检测 {actual_duration:.1f}s）"
            # status_msg += f"，有效检测{valid_detections}个（{frames_with_targets}帧有目标），已批量写入label"
            #
            # rospy.loginfo(status_msg)
        except Exception as e:
            rospy.logwarn(f"⚠️ 批量写入label失败: {e}")
        
        # 检测结束后只缓存检测结果，不进行空间计算（优化FPS）
        return all_detections
