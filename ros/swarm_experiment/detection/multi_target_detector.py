#!/usr/bin/env python3
"""
多目标检测模块
负责协调多个摄像头的检测任务
"""

import rospy
import threading
from .camera_detector import CameraDetector

class MultiTargetDetector:
    """多目标检测器"""
    
    def __init__(self, config):
        self.config = config
        self.camera_detector = CameraDetector(config)
        self.all_camera_detections = {}
        
    def start_2d_detection(self):
        """启动2D空间检测 - 支持多目标"""
        rospy.loginfo("🎥 启动2D空间检测（多目标模式）...")

        # 清空之前的检测结果
        self.all_camera_detections = {}  # 清空检测结果缓存
        
        # 启动多摄像头检测
        detection_threads = []
        detection_results = {}
        
        def detect_wrapper(camera):
            """检测包装函数，用于收集结果"""
            cam_name = camera.split('/')[-2]
            result = self.camera_detector.detect_camera_2d(camera)
            detection_results[cam_name] = result
        
        for camera in self.config.config['cameras']:
            thread = threading.Thread(target=detect_wrapper, args=(camera,))
            thread.start()
            detection_threads.append(thread)
        
        # 等待所有检测线程完成
        for thread in detection_threads:
            thread.join()
        
        # 收集所有检测结果
        self.all_camera_detections = detection_results
        
        # 记录缓存状态
        for cam_name, detections in self.all_camera_detections.items():
            rospy.loginfo(f"📦 {cam_name}: 检测结果已缓存，等待统一分析")
        
        return self.all_camera_detections
    
    def get_detection_results(self):
        """获取检测结果"""
        return self.all_camera_detections
    
    def clear_detection_results(self):
        """清空检测结果"""
        self.all_camera_detections = {}
