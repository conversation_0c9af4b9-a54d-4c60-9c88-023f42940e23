# 集群实验系统 - 模块化架构

## 📁 项目结构

```
swarm_experiment/
├── __init__.py                    # 包初始化
├── main.py                        # 主程序入口
├── README.md                      # 项目说明
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── config.py                  # 配置管理 (90行)
│   └── experiment_controller.py   # 实验控制器 (200行)
├── detection/                     # 检测模块
│   ├── __init__.py
│   ├── camera_detector.py         # 单摄像头检测 (150行)
│   └── multi_target_detector.py   # 多目标检测协调 (50行)
├── analysis/                      # 分析模块
│   ├── __init__.py
│   ├── flash_analyzer.py          # 灯语分析 (70行)
│   ├── flash_patterns.py          # 闪烁模式分析 (200行) [原 newan.py]
│   └── spatial_analyzer.py        # 空间分析 (120行)
├── behavior/                      # 行为模块
│   ├── __init__.py
│   └── swarm_behavior.py          # 集群行为执行 (200行)
└── utils/                         # 工具模块
    ├── __init__.py
    ├── ros_utils.py               # ROS工具函数 (40行)
    └── camera_utils.py            # 相机工具函数 (300行) [原 cal_vec.py]
```

## 🔄 重构对比

### 重构前：
- **单文件**：V6simple_2d_swarm_experiment.py (1243行)
- **外部依赖**：newan.py, cal_vec.py
- **问题**：代码混乱、难以维护、职责不清、依赖分散

### 重构后：
- **模块化**：10个专门的模块文件
- **自包含**：所有依赖都在项目内部
- **平均文件长度**：100-200行
- **清晰职责**：每个模块负责特定功能
- **规范命名**：使用更清晰的模块名称

## 🚀 使用方法

### 运行重构版本：
```bash
cd /home/<USER>/Downloads/ros
python3 V6simple_2d_swarm_experiment_refactored.py
```

### 运行原版本（对比）：
```bash
cd /home/<USER>/Downloads/ros
python3 V6simple_2d_swarm_experiment.py
```

## 📋 模块说明

### 1. 核心模块 (core/)
- **config.py**: 管理所有配置参数
- **experiment_controller.py**: 协调各模块，实现主要实验流程

### 2. 检测模块 (detection/)
- **camera_detector.py**: 单摄像头的YOLO检测和跟踪
- **multi_target_detector.py**: 多摄像头检测任务协调

### 3. 分析模块 (analysis/)
- **flash_analyzer.py**: 灯语模式分析和指令映射
- **flash_patterns.py**: 闪烁模式分析核心算法 (原 newan.py)
- **spatial_analyzer.py**: 空间位置计算和目标选择

### 4. 行为模块 (behavior/)
- **swarm_behavior.py**: 各种集群行为的具体实现

### 5. 工具模块 (utils/)
- **ros_utils.py**: ROS发布者、订阅者等通用功能
- **camera_utils.py**: 相机内参、空间计算等工具 (原 cal_vec.py)

## ✅ 重构优势

1. **可维护性**: 每个文件职责单一，易于理解和修改
2. **可测试性**: 可以单独测试每个模块
3. **可复用性**: 模块可以在其他项目中复用
4. **团队协作**: 多人可以同时修改不同模块
5. **扩展性**: 新功能可以作为新模块添加

## 🔧 开发指南

### 添加新功能：
1. 确定功能属于哪个模块
2. 在对应模块中添加方法
3. 在experiment_controller中调用

### 修改配置：
1. 在config.py中修改参数
2. 所有模块自动使用新配置

### 调试单个模块：
```python
from swarm_experiment.detection.camera_detector import CameraDetector
# 单独测试检测模块
```

## 📊 性能对比

| 指标 | 原版本 | 重构版本 |
|------|--------|----------|
| 文件数量 | 1个 | 9个 |
| 最大文件行数 | 1243行 | 200行 |
| 代码复用性 | 低 | 高 |
| 维护难度 | 高 | 低 |
| 测试便利性 | 差 | 好 |
