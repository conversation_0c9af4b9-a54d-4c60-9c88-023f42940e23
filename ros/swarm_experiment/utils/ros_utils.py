#!/usr/bin/env python3
"""
ROS工具函数模块
提供ROS相关的通用功能
"""

import rospy
import numpy as np
import time
from std_msgs.msg import Int32
from geometry_msgs.msg import Twist
from .camera_utils import create_camera_matrix

class ROSUtils:
    """ROS工具类"""
    
    def __init__(self, config):
        self.config = config
        self.setup_publishers()
        # 相机矩阵已在config中设置，不需要重复设置
        
    def setup_publishers(self):
        """设置ROS发布者"""
        # 使用与原版本相同的话题命名方式
        led_topic = f'/{self.config.node_name}/led_mode'
        vel_topic = f'/{self.config.node_name}/vel_cmd'

        self.vel_pub = rospy.Publisher(vel_topic, Twist, queue_size=10)
        self.led_pub = rospy.Publisher(led_topic, Int32, queue_size=10)

        rospy.loginfo(f"📡 设置发布者:")
        rospy.loginfo(f"   LED话题: {led_topic}")
        rospy.loginfo(f"   速度话题: {vel_topic}")
        rospy.loginfo("✅ ROS发布者设置完成")

        time.sleep(1)  # 与原版本保持一致
        

            
    def send_led_command(self, mode):
        """发送LED指令"""
        msg = Int32()
        msg.data = mode
        self.led_pub.publish(msg)
        action = self.config.SWARM_COMMANDS.get(mode, "未知")
        rospy.loginfo(f"🔆 发送LED指令: 模式{mode} → {action}")
        
    def get_publishers(self):
        """获取发布者"""
        return {
            'velocity': self.vel_pub,
            'led': self.led_pub
        }
