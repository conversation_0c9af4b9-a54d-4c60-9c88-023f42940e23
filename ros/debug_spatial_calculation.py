#!/usr/bin/env python3
"""
调试空间计算问题
"""

import sys
import os
import numpy as np

# 添加swarm_experiment模块到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'swarm_experiment'))

def test_camera_matrix():
    """测试相机矩阵"""
    print("🔍 测试相机矩阵...")
    
    try:
        from swarm_experiment.core.config import SwarmConfig
        config = SwarmConfig()
        
        print(f"相机矩阵状态: {config.camera_matrix_inv is not None}")
        if config.camera_matrix_inv is not None:
            print(f"矩阵形状: {config.camera_matrix_inv.shape}")
            print(f"矩阵内容:\n{config.camera_matrix_inv}")
        
        return config
    except Exception as e:
        print(f"❌ 相机矩阵测试失败: {e}")
        return None

def test_spatial_calculation():
    """测试空间计算"""
    print("\n🔍 测试空间计算...")
    
    try:
        from swarm_experiment.utils.camera_utils import calculate_position_vector_planar, create_camera_matrix
        
        # 创建测试数据
        camera_matrix = create_camera_matrix()
        camera_matrix_inv = np.linalg.inv(camera_matrix)
        
        # 测试参数（模拟您的实际数据）
        width = 0.1  # 归一化宽度
        x_center = 0.5  # 归一化X中心
        y_center = 0.5  # 归一化Y中心
        real_width = 0.31  # 真实宽度
        
        print(f"测试参数:")
        print(f"  width: {width}")
        print(f"  x_center: {x_center}")
        print(f"  y_center: {y_center}")
        print(f"  real_width: {real_width}")
        
        # 计算位置
        position_vector, distance, theta, azimuth, elevation = calculate_position_vector_planar(
            width, x_center, y_center, camera_matrix_inv, real_width
        )
        
        print(f"\n计算结果:")
        print(f"  距离: {distance:.2f}m")
        print(f"  方位: {azimuth:.1f}°")
        print(f"  位置: [{position_vector[0]:.2f}, {position_vector[1]:.2f}]")
        
        # 测试不同宽度的影响
        print(f"\n测试不同宽度:")
        for test_width in [0.05, 0.1, 0.2, 0.3]:
            _, test_distance, _, test_azimuth, _ = calculate_position_vector_planar(
                test_width, x_center, y_center, camera_matrix_inv, real_width
            )
            print(f"  宽度{test_width}: 距离{test_distance:.2f}m, 方位{test_azimuth:.1f}°")
        
        return True
        
    except Exception as e:
        print(f"❌ 空间计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_detection_data_format():
    """测试检测数据格式"""
    print("\n🔍 测试检测数据格式...")
    
    # 模拟检测数据格式
    test_detection = (1, 2, 0.5, 0.5, 0.1, 0.1)  # (track_id, class_id, x, y, w, h)
    
    print(f"检测数据: {test_detection}")
    
    # 提取空间计算需要的数据
    track_id, class_id, x_center, y_center, width, height = test_detection
    
    print(f"提取的数据:")
    print(f"  track_id: {track_id}")
    print(f"  class_id: {class_id}")
    print(f"  x_center: {x_center}")
    print(f"  y_center: {y_center}")
    print(f"  width: {width}")
    print(f"  height: {height}")
    
    return (x_center, y_center, width, height)

def main():
    """主函数"""
    print("🚀 空间计算问题诊断...")
    print("=" * 50)
    
    # 1. 测试相机矩阵
    config = test_camera_matrix()
    
    # 2. 测试空间计算
    spatial_ok = test_spatial_calculation()
    
    # 3. 测试数据格式
    detection_data = test_detection_data_format()
    
    print("\n" + "=" * 50)
    if config and spatial_ok:
        print("✅ 基础组件正常")
        print("💡 可能的问题:")
        print("1. 检查实际检测数据中的width值是否过小")
        print("2. 检查数据格式是否正确")
        print("3. 检查相机矩阵是否在运行时被修改")
    else:
        print("❌ 发现问题，需要进一步调试")

if __name__ == '__main__':
    main()
